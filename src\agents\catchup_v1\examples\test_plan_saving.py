#!/usr/bin/env python3
"""Test script for the plan saving functionality.

This script demonstrates how to use the save_plan_to_markdown tool
to save plans to the filesystem in Markdown format for testing purposes.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent.parent.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from agents.catchup_v1.graph import create_catchup_v1_graph
from agents.catchup_v1.state import CatchUpV1State
from langchain_core.messages import HumanMessage


async def test_plan_saving():
    """Test the plan saving functionality."""
    
    print("🧪 Testing Plan Saving Functionality")
    print("=" * 50)
    
    # Create the graph
    graph = create_catchup_v1_graph()
    
    # Create initial state with a complex request that will trigger planning
    initial_state = CatchUpV1State(
        messages=[
            HumanMessage(content="I need to find a restaurant, book a table for 4 people tonight at 7pm, and send confirmation details to my email. Also, search for nearby parking options and send me the best deals.")
        ],
        user_context={
            "user_id": "test_user_123",
            "email_address": "<EMAIL>",
            "location": {
                "latitude": 45.4642,
                "longitude": 9.1900
            },
            "current_session_start": "2024-01-15T10:30:00Z"
        },
        session_config={
            "session_id": "test_session_001",
            "auto_planning_enabled": True,
            "communication_enabled": True
        }
    )
    
    # Configuration for the agent
    config = {
        "configurable": {
            "model_name": "openai/gpt-4o-mini",
            "model_temperature": 0.1,
            "auto_planning_enabled": True,
            "debug_mode": True
        }
    }
    
    print("📝 Step 1: Creating a plan...")
    
    try:
        # Run the agent to create a plan
        result = await graph.ainvoke(initial_state, config)
        
        # Check if a plan was created
        current_plan = result.get("current_plan", [])
        if current_plan:
            print(f"✅ Plan created successfully with {len(current_plan)} tasks")
            
            # Print plan summary
            print("\n📋 Plan Summary:")
            for i, task in enumerate(current_plan, 1):
                status_emoji = {
                    "pending": "⏳",
                    "in_progress": "🔄", 
                    "completed": "✅",
                    "failed": "❌"
                }.get(task.get("status", "pending"), "❓")
                
                priority = task.get("priority", "medium")
                print(f"  {i}. {status_emoji} {task.get('content', 'No description')} [{priority.upper()}]")
            
            print("\n💾 Step 2: Saving plan to Markdown...")
            
            # Now test the save functionality by sending a message to use the tool
            save_request_state = CatchUpV1State(
                messages=result["messages"] + [
                    HumanMessage(content="Please save the current plan to a markdown file for testing purposes.")
                ],
                current_plan=current_plan,
                progress_metrics=result.get("progress_metrics", {}),
                user_context=result.get("user_context", {}),
                session_config=result.get("session_config", {}),
                conversation_phase=result.get("conversation_phase", {})
            )
            
            # Run the agent again to trigger the save tool
            save_result = await graph.ainvoke(save_request_state, config)
            
            # Check the last message for save confirmation
            last_message = save_result["messages"][-1]
            print(f"📄 Save result: {last_message.content}")
            
            # Check if plans directory was created
            plans_dir = Path("plans")
            if plans_dir.exists():
                plan_files = list(plans_dir.glob("*.md"))
                if plan_files:
                    print(f"✅ Found {len(plan_files)} plan file(s) in plans directory:")
                    for plan_file in plan_files:
                        print(f"  📁 {plan_file}")
                        
                        # Show a preview of the file content
                        with open(plan_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            lines = content.split('\n')
                            print(f"  📖 Preview (first 10 lines):")
                            for line in lines[:10]:
                                print(f"     {line}")
                            if len(lines) > 10:
                                print(f"     ... ({len(lines) - 10} more lines)")
                else:
                    print("❌ No plan files found in plans directory")
            else:
                print("❌ Plans directory was not created")
                
        else:
            print("❌ No plan was created")
            print("💡 This might happen if the request was too simple or direct")
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")


async def test_direct_tool_usage():
    """Test the save_plan_to_markdown tool directly."""
    
    print("\n🔧 Testing Direct Tool Usage")
    print("=" * 50)
    
    from agents.catchup_v1.tools.planning import save_plan_to_markdown
    from langchain_core.tools import InjectedToolCallId
    from langgraph.prebuilt import InjectedState
    
    # Create a mock state with a plan
    mock_state = {
        "current_plan": [
            {
                "id": "task_1",
                "content": "Find a suitable restaurant",
                "status": "completed",
                "priority": "high",
                "estimated_duration": 120,
                "actual_duration": 95,
                "tools_required": ["search_deals", "get_all_categories"],
                "dependencies": [],
                "created_at": "2024-01-15T10:30:00Z",
                "started_at": "2024-01-15T10:30:05Z",
                "completed_at": "2024-01-15T10:31:40Z",
                "error_message": None
            },
            {
                "id": "task_2", 
                "content": "Book a table for 4 people at 7pm",
                "status": "in_progress",
                "priority": "critical",
                "estimated_duration": 180,
                "actual_duration": None,
                "tools_required": ["create_booking"],
                "dependencies": ["task_1"],
                "created_at": "2024-01-15T10:30:00Z",
                "started_at": "2024-01-15T10:31:45Z",
                "completed_at": None,
                "error_message": None
            },
            {
                "id": "task_3",
                "content": "Send confirmation email to user",
                "status": "pending",
                "priority": "medium",
                "estimated_duration": 60,
                "actual_duration": None,
                "tools_required": ["sent_email_to_users"],
                "dependencies": ["task_2"],
                "created_at": "2024-01-15T10:30:00Z",
                "started_at": None,
                "completed_at": None,
                "error_message": None
            }
        ],
        "progress_metrics": {
            "total_tasks": 3,
            "completed_tasks": 1,
            "failed_tasks": 0
        },
        "active_task_id": "task_2",
        "user_context": {
            "user_id": "direct_test_user",
            "email_address": "<EMAIL>",
            "location": {"latitude": 45.4642, "longitude": 9.1900}
        },
        "session_config": {
            "session_id": "direct_test_session",
            "auto_planning_enabled": True
        }
    }
    
    try:
        # Test the tool directly
        print("📝 Calling save_plan_to_markdown tool directly...")
        
        # Note: This is a simplified test - in real usage, the tool would be called
        # by the LLM with proper injection of state and tool_call_id
        result = save_plan_to_markdown._func(
            state=mock_state,
            tool_call_id="test_call_123",
            filename="direct_test_plan",
            directory="test_plans"
        )
        
        print(f"✅ Tool execution result: {result}")
        
        # Check if the file was created
        test_file = Path("test_plans/direct_test_plan.md")
        if test_file.exists():
            print(f"✅ File created: {test_file.absolute()}")
            
            # Show file content
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 File content preview:")
                lines = content.split('\n')
                for i, line in enumerate(lines[:15], 1):
                    print(f"  {i:2d}: {line}")
                if len(lines) > 15:
                    print(f"      ... ({len(lines) - 15} more lines)")
        else:
            print(f"❌ File was not created: {test_file}")
            
    except Exception as e:
        print(f"❌ Error during direct tool test: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 Starting Plan Saving Tests")
    print("=" * 60)
    
    # Run the tests
    asyncio.run(test_plan_saving())
    asyncio.run(test_direct_tool_usage())
    
    print("\n🎉 All tests completed!")
    print("📁 Check the 'plans' and 'test_plans' directories for generated files.")
