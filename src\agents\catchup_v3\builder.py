from catchup_v3.sub_agent import _create_task_tool, SubAgent
from catchup_v3.llm_model import get_default_model

from catchup_v3.state import BuilderState
from typing import Sequence, Union, Callable, Any, TypeVar, Type, Optional
from langchain_core.tools import BaseTool
from langchain_core.language_models import LanguageModelLike

from langgraph.prebuilt import create_react_agent
from langgraph.graph import StateGraph, START, END

from catchup_v3.internal_tools import write_todos,write_file, read_file, ls, edit_file

StateSchema = TypeVar("StateSchema", bound=BuilderState)
StateSchemaType = Type[StateSchema]

base_prompt = """You have access to a number of standard tools

## `write_todos`

You have access to the `write_todos` tools to help you manage and plan tasks. Use these tools VERY frequently to ensure that you are tracking your tasks and giving the user visibility into your progress.
These tools are also EXTREMELY helpful for planning tasks, and for breaking down larger complex tasks into smaller steps. If you do not use this tool when planning, you may forget to do important tasks - and that is unacceptable.

It is critical that you mark todos as completed as soon as you are done with a task. Do not batch up multiple tasks before marking them as completed.
## `task`

- When doing web search, prefer to use the `task` tool in order to reduce context usage."""



def build_plan_execute_agent(
    tools: Sequence[Union[BaseTool, Callable, dict[str, Any]]],
    instructions: str,
    model: Optional[Union[str, LanguageModelLike]] = None,
    subagents: list[SubAgent] = None,
    state_schema: Optional[StateSchemaType] = None,
    enable_replanning: bool = True,
):
    """Create explicit build_plan_execute_agent agent with segregated nodes.

    This a Plane and Execute builder  with explicit:
    - Planner node (generates initial plan)
    - Executor node (executes tasks one by one)
    - Replanner node (modifies plan based on results)

    Args:
        tools: Additional tools for the agent
        instructions: System instructions for all nodes
        model: LLM model to use
        subagents: List of subagents for task delegation
        state_schema: State schema (defaults to BuilderState)
        enable_replanning: Whether to enable replanning capability

    Returns:
        Compiled LangGraph workflow
    """

    # Setup tools and model
    built_in_tools = [write_todos, write_file, read_file, ls, edit_file]
    if model is None:
        model = get_default_model()
    state_schema = state_schema or BuilderState

    # Create task delegation tool
    task_tool = _create_task_tool(
        list(tools) + built_in_tools,
        instructions,
        subagents or [],
        model,
        state_schema
    )
    all_tools = built_in_tools + list(tools) + [task_tool]

    # Internal planner/executor/replanner nodes as ReAct agents
    def _create_planner_node():
        planner_prompt = f"""You are a strategic planner. Break down user requests into actionable tasks.

        {instructions}

        Create a structured plan using write_todos. Be specific and actionable.
        Focus on creating a clear sequence of tasks that can be executed independently."""

        return create_react_agent(
            model,
            prompt=planner_prompt,
            tools=[write_todos] + built_in_tools,
            state_schema=state_schema
        )
    
    def _create_executor_node():
        executor_prompt = f"""You are a task executor. Execute one task at a time from the plan.

        {instructions}

        Look at the current todos and execute the next pending task.
        Use the task tool to delegate to subagents when appropriate.
        Mark tasks as completed when finished using write_todos."""

        return create_react_agent(
            model,
            prompt=executor_prompt,
            tools=all_tools,  # Full tool access including task delegation
            state_schema=state_schema
        )

    def _create_replanner_node():
        replanner_prompt = f"""You are a replanner. Analyze completed tasks and update the remaining plan.

        {instructions}

        Review the current plan and execution results. If needed:
        1. Add new tasks based on discoveries
        2. Modify existing tasks based on results
        3. Remove unnecessary tasks
        Use write_todos to update the plan."""

        return create_react_agent(
            model,
            prompt=replanner_prompt,
            tools=[write_todos] + built_in_tools,
            state_schema=state_schema
        )
    
    # Routing functions
    def should_continue_execution(state):
        """Determine if there are more tasks to execute."""
        todos = state.get("todos", [])
        pending_tasks = [todo for todo in todos if todo["status"] == "pending"]
        in_progress_tasks = [todo for todo in todos if todo["status"] == "in_progress"]

        if pending_tasks or in_progress_tasks:
            return "executor"
        return "end"

    def should_replan(state):
        """Determine if replanning is needed after execution."""
        if not enable_replanning:
            return should_continue_execution(state)

        todos = state.get("todos", [])
        completed_tasks = [todo for todo in todos if todo["status"] == "completed"]

        # Replan every 3 completed tasks or if there are failed tasks
        if len(completed_tasks) % 3 == 0 and len(completed_tasks) > 0:
            return "replanner"

        # Check for any indication that replanning might be needed
        # (This could be enhanced with more sophisticated logic)
        return should_continue_execution(state)

    # Build the explicit graph
    workflow = StateGraph(state_schema)
    workflow.add_node("planner", _create_planner_node())
    workflow.add_node("executor", _create_executor_node())
    if enable_replanning:
        workflow.add_node("replanner", _create_replanner_node())

    # Add routing logic
    workflow.add_edge(START, "planner")

    if enable_replanning:
        # Planner -> Executor -> (Replanner | End) -> Executor -> ...
        workflow.add_edge("planner", "executor")
        workflow.add_conditional_edges(
            "executor",
            should_replan,
            {
                "executor": "executor",
                "replanner": "replanner",
                "end": END
            }
        )
        workflow.add_conditional_edges(
            "replanner",
            should_continue_execution,
            {
                "executor": "executor",
                "end": END
            }
        )
    else:
        # Simple Planner -> Executor -> Executor -> ... -> End
        workflow.add_edge("planner", "executor")
        workflow.add_conditional_edges(
            "executor",
            should_continue_execution,
            {
                "executor": "executor",
                "end": END
            }
        )

    return workflow.compile()


