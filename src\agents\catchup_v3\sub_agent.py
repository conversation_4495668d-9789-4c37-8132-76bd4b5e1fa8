from deepagents.prompts import TASK_DESCRIPTION_PREFIX, TASK_DESCRIPTION_SUFFIX
from deepagents.state import DeepAgentState
from langgraph.prebuilt import create_react_agent
from langchain_core.tools import BaseTool
from typing import TypedDict
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from typing import Annotated, NotRequired
from langgraph.types import Command

from langgraph.prebuilt import InjectedState


class SubAgent(TypedDict):
    name: str
    description: str
    prompt: str
    tools: NotRequired[list[str]]
    # New HIL configuration
    require_approval: NotRequired[list[str]]  # Actions requiring approval
    approval_threshold: NotRequired[str]      # "low" | "medium" | "high"


def _create_task_tool(tools, instructions, subagents: list[SubAgent], model, state_schema):
    agents = {
        "general-purpose": create_react_agent(model, prompt=instructions, tools=tools)
    }
    tools_by_name = {}
    for tool_ in tools:
        if not isinstance(tool_, BaseTool):
            tool_ = tool(tool_)
        tools_by_name[tool_.name] = tool_
 
    # Add HIL tool to available tools
    hil_tools = [request_human_approval]
    
    for _agent in subagents:
        if "tools" in _agent:
            _tools = [tools_by_name[t] for t in _agent["tools"]]
        else:
            _tools = tools
            
        # Add HIL capability if configured
        if _agent.get("require_approval"):
            _tools = _tools + hil_tools
            
            # Enhance prompt with HIL instructions
            hil_prompt = f"""
            
HUMAN APPROVAL REQUIRED for: {_agent.get('require_approval', [])}

Before executing any of these actions, you MUST call request_human_approval with:
- Clear description of what you want to do
- Specific proposed action
- Your reasoning for why this action is needed

Wait for human approval before proceeding."""
            
            enhanced_prompt = _agent["prompt"] + hil_prompt
        else:
            enhanced_prompt = _agent["prompt"]
            
        agents[_agent["name"]] = create_react_agent(
            model, 
            prompt=enhanced_prompt, 
            tools=_tools, 
            state_schema=state_schema
        )

    other_agents_string = [
        f"- {_agent['name']}: {_agent['description']}" for _agent in subagents
    ]

    @tool(
        description=TASK_DESCRIPTION_PREFIX.format(other_agents=other_agents_string)
        + TASK_DESCRIPTION_SUFFIX
    )
    def task(
        description: str,
        subagent_type: str,
        state: Annotated[DeepAgentState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ):
        if subagent_type not in agents:
            return f"Error: invoked agent of type {subagent_type}, the only allowed types are {[f'`{k}`' for k in agents]}"
        sub_agent = agents[subagent_type]
        state["messages"] = [{"role": "user", "content": description}]
        result = sub_agent.invoke(state)
        return Command(
            update={
                "files": result.get("files", {}),
                "messages": [
                    ToolMessage(
                        result["messages"][-1].content, tool_call_id=tool_call_id
                    )
                ],
            }
        )

    return task

