# Explicit Plan-Execute-Replan Architecture

## Overview

The `build_explicit_plan_execute_agent` provides a simple interface for creating sophisticated plan-execute-replan workflows with subagent delegation capabilities.

## Architecture Diagram

```mermaid
flowchart TD
    Start([User Request]) --> Planner[🧠 Planner Node]
    
    Planner --> |"write_todos()<br/>Creates initial plan"| Executor[⚡ Executor Node]
    
    Executor --> |"Executes next task"| TaskType{Task Type}
    
    TaskType --> |🤖 Delegate| SubagentCall["task() tool<br/>🎯 Subagent Selection"]
    TaskType --> |🔧 Direct| DirectExec[Direct Execution]
    
    SubagentCall --> SubagentChoice{Which Subagent?}
    
    SubagentChoice --> |Research| SA1[🔍 Research Agent]
    SubagentChoice --> |Analysis| SA2[📊 Analysis Agent] 
    SubagentChoice --> |Writing| SA3[✍️ Writing Agent]
    SubagentChoice --> |Custom| SAn[🎨 Custom Agent N]
    
    SA1 --> TaskResult[Task Result]
    SA2 --> TaskResult
    SA3 --> TaskResult
    SAn --> TaskResult
    DirectExec --> TaskResult
    
    TaskResult --> TaskCheck{Task Status}
    
    TaskCheck --> |✅ Completed| ReplanCheck{Replanning<br/>Enabled?}
    TaskCheck --> |❌ Failed| ReplanCheck
    TaskCheck --> |⏳ More pending| Executor
    
    ReplanCheck --> |🔄 Yes<br/>Every 3 tasks<br/>or failures| Replanner[🔄 Replanner Node]
    ReplanCheck --> |➡️ No| ContinueCheck{More Tasks?}
    
    Replanner --> |"write_todos()<br/>Updates plan"| ContinueCheck
    
    ContinueCheck --> |✅ Yes| Executor
    ContinueCheck --> |🏁 No| End([Complete])
    
    %% Styling
    classDef nodeStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef decisionStyle fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef startEndStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef subagentStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef delegationStyle fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    
    class Planner,Executor,Replanner nodeStyle
    class TaskCheck,ReplanCheck,ContinueCheck,TaskType,SubagentChoice decisionStyle
    class Start,End startEndStyle
    class SA1,SA2,SA3,SAn subagentStyle
    class SubagentCall,DirectExec,TaskResult delegationStyle
    
    %% Subagent definitions box
    subgraph SubagentDefs [" 🤖 Injected Subagents (N) "]
        direction TB
        D1["🔍 Research Agent<br/>description: 'Conducts research'<br/>tools: [search, analyze]"]
        D2["📊 Analysis Agent<br/>description: 'Data analysis'<br/>tools: [calculate, visualize]"]
        D3["✍️ Writing Agent<br/>description: 'Content creation'<br/>tools: [write, edit]"]
        Dn["🎨 Custom Agent N<br/>description: 'User-defined'<br/>tools: [custom_tools]"]
    end
    
    classDef defStyle fill:#f1f8e9,stroke:#689f38,stroke-width:1px
    class D1,D2,D3,Dn defStyle
```

## Key Components

### 🧠 Planner Node
- Receives user requests
- Breaks down complex tasks into actionable steps
- Uses `write_todos()` to create structured plans
- Each task has status: "pending", "in_progress", "completed"

### ⚡ Executor Node
- Executes tasks from the plan sequentially
- **Intelligent Task Routing**:
  - Direct execution for simple tasks
  - Subagent delegation for specialized tasks
- Uses `task()` tool for subagent selection
- Updates task status upon completion

### 🔄 Replanner Node (Optional)
- Analyzes execution results and outcomes
- **Replanning Triggers**:
  - Every 3 completed tasks
  - Task failures
  - Unexpected discoveries
- Updates plan using `write_todos()`

### 🤖 Subagent Injection
- **N Subagents**: Inject any number of specialized agents
- **Dynamic Selection**: Executor chooses appropriate subagent based on task description
- **Custom Tools**: Each subagent has domain-specific tools
- **Modular Design**: Easy to add/remove specialized capabilities

## Usage Example

```python
from agents.catchup_v3.builder import build_explicit_plan_execute_agent

# Define specialized subagents
research_agent = {
    "name": "researcher",
    "description": "Conducts thorough research on any topic",
    "prompt": "You are a research specialist...",
    "tools": ["internet_search", "document_analyzer"]
}

analysis_agent = {
    "name": "analyst", 
    "description": "Performs data analysis and creates insights",
    "prompt": "You are a data analyst...",
    "tools": ["calculate", "visualize", "statistical_analysis"]
}

# Create the agent
agent = build_explicit_plan_execute_agent(
    tools=[custom_tool1, custom_tool2],
    instructions="You are a comprehensive research and analysis assistant.",
    subagents=[research_agent, analysis_agent],
    enable_replanning=True
)

# Use the agent
result = agent.invoke({
    "messages": [{"role": "user", "content": "Research renewable energy trends and create a comprehensive analysis report"}]
})
```

## Benefits

### 🎯 Simple Interface
- Same signature as existing builders
- Users only define subagents
- Builder handles all complexity

### 🏗️ Explicit Architecture
- Clear separation of planning, execution, and replanning
- Easy to debug and understand
- Transparent task flow

### 🔄 Adaptive Planning
- Dynamic plan updates based on results
- Failure recovery through replanning
- Continuous improvement of task execution

### 🤖 Flexible Delegation
- Automatic subagent selection
- Specialized tool access per subagent
- Scalable to N specialized agents

## Configuration Options

- **enable_replanning**: Toggle replanning capability
- **tools**: Additional tools for all nodes
- **subagents**: List of specialized subagents
- **model**: LLM model for all nodes
- **state_schema**: Custom state schema

This architecture provides the perfect balance of **simplicity for users** and **sophisticated capabilities** for complex multi-step workflows with specialized subagent delegation.
