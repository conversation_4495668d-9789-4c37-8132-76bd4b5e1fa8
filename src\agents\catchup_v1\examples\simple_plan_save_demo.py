#!/usr/bin/env python3
"""Simple demonstration of the plan saving feature.

This script shows the most basic usage of the plan saving functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent.parent.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from agents.catchup_v1.graph import create_catchup_v1_graph
from agents.catchup_v1.state import CatchUpV1State
from langchain_core.messages import HumanMessage


async def demo_plan_saving():
    """Simple demo of plan saving functionality."""
    
    print("🎯 Simple Plan Saving Demo")
    print("=" * 40)
    
    # Create the agent graph
    graph = create_catchup_v1_graph()
    
    # Create a request that will trigger planning
    state = CatchUpV1State(
        messages=[
            HumanMessage(content="I need to find a good Italian restaurant, book a table for 2 people tonight, and get directions to the location.")
        ],
        user_context={
            "user_id": "demo_user",
            "email_address": "<EMAIL>"
        },
        session_config={
            "session_id": "demo_session",
            "auto_planning_enabled": True
        }
    )
    
    config = {
        "configurable": {
            "model_name": "openai/gpt-4o-mini",
            "auto_planning_enabled": True
        }
    }
    
    print("📋 Creating plan...")
    
    try:
        # Step 1: Create the plan
        result = await graph.ainvoke(state, config)
        
        if result.get("current_plan"):
            print(f"✅ Plan created with {len(result['current_plan'])} tasks")
            
            # Step 2: Ask to save the plan
            save_state = CatchUpV1State(
                messages=result["messages"] + [
                    HumanMessage(content="Save this plan to a markdown file called 'demo_plan'")
                ],
                current_plan=result["current_plan"],
                progress_metrics=result.get("progress_metrics", {}),
                user_context=result.get("user_context", {}),
                session_config=result.get("session_config", {})
            )
            
            print("💾 Saving plan...")
            save_result = await graph.ainvoke(save_state, config)
            
            # Check the result
            last_message = save_result["messages"][-1]
            print(f"📄 Result: {last_message.content}")
            
            # Look for the saved file
            plans_dir = Path("plans")
            if plans_dir.exists():
                md_files = list(plans_dir.glob("demo_plan*.md"))
                if md_files:
                    print(f"✅ File saved: {md_files[0]}")
                    print("📖 You can now open the file to see the plan in Markdown format!")
                else:
                    print("❓ No demo_plan file found, but other files might exist:")
                    for f in plans_dir.glob("*.md"):
                        print(f"  📁 {f}")
            
        else:
            print("❌ No plan was created")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n🏁 Demo completed!")


if __name__ == "__main__":
    asyncio.run(demo_plan_saving())
