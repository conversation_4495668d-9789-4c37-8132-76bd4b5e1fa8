from langgraph.prebuilt.chat_agent_executor import AgentState
from typing import NotRequired, Annotated
from catchup_v3.models import Todo


def file_reducer(l, r):
    if l is None:
        return r
    elif r is None:
        return l
    else:
        return {**l, **r}

class BuilderState(AgentState):
    todos: NotRequired[list[Todo]]
    files: Annotated[NotRequired[dict[str, str]], file_reducer]
