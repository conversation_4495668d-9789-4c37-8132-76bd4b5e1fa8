
from dotenv import load_dotenv

from catchup_v3.builder import build_plan_execute_agent
from catchup_v3.tools.internet_search import internet_search
from catchup_v3.llm_model import get_default_model
from catchup_v3.state import BuilderState

from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import create_react_agent
from langchain_core.messages import HumanMessage

load_dotenv()
sub_research_prompt = """You are a dedicated researcher. Your job is to conduct research based on the users questions.

Conduct thorough research and then reply to the user with a detailed answer to their question

only your FINAL answer will be passed on to the user. They will have NO knowledge of anything expect your final message, so your final report should be your final message!"""

# Prompt prefix to steer the agent to be an expert researcher
main_prompt = """You are an expert researcher. Your job is to conduct thorough research, and then write a polished report.

The first thing you should do is to write the original user question to `question.txt` so you have a record of it.

Use the research-agent to conduct deep research. It will respond to your questions/topics with a detailed answer.

When you think you enough information to write a final report, write it to `final_report.md`

You can call the critique-agent to get a critique of the final report. After that (if needed) you can do more research and edit the `final_report.md`
You can do this however many times you want until are you satisfied with the result.

Only edit the file once at a time (if you call this tool in parallel, there may be conflicts).

Here are instructions for writing the final report:

<report_instructions>

CRITICAL: Make sure the answer is written in the same language as the human messages! If you make a todo plan - you should note in the plan what language the report should be in so you dont forget!
Note: the language the report should be in is the language the QUESTION is in, not the language/country that the question is ABOUT.

Please create a detailed answer to the overall research brief that:
1. Is well-organized with proper headings (# for title, ## for sections, ### for subsections)
2. Includes specific facts and insights from the research
3. References relevant sources using [Title](URL) format
4. Provides a balanced, thorough analysis. Be as comprehensive as possible, and include all information that is relevant to the overall research question. People are using you for deep research and will expect detailed, comprehensive answers.
5. Includes a "Sources" section at the end with all referenced links

You can structure your report in a number of different ways. Here are some examples:

To answer a question that asks you to compare two things, you might structure your report like this:
1/ intro
2/ overview of topic A
3/ overview of topic B
4/ comparison between A and B
5/ conclusion

To answer a question that asks you to return a list of things, you might only need a single section which is the entire list.
1/ list of things or table of things
Or, you could choose to make each item in the list a separate section in the report. When asked for lists, you don't need an introduction or conclusion.
1/ item 1
2/ item 2
3/ item 3

To answer a question that asks you to summarize a topic, give a report, or give an overview, you might structure your report like this:
1/ overview of topic
2/ concept 1
3/ concept 2
4/ concept 3
5/ conclusion

If you think you can answer the question with a single section, you can do that too!
1/ answer

REMEMBER: Section is a VERY fluid and loose concept. You can structure your report however you think is best, including in ways that are not listed above!
Make sure that your sections are cohesive, and make sense for the reader.

For each section of the report, do the following:
- Use simple, clear language
- Use ## for section title (Markdown format) for each section of the report
- Do NOT ever refer to yourself as the writer of the report. This should be a professional report without any self-referential language. 
- Do not say what you are doing in the report. Just write the report without any commentary from yourself.
- Each section should be as long as necessary to deeply answer the question with the information you have gathered. It is expected that sections will be fairly long and verbose. You are writing a deep research report, and users will expect a thorough answer.
- Use bullet points to list out information when appropriate, but by default, write in paragraph form.

REMEMBER:
The brief and research may be in English, but you need to translate this information to the right language when writing the final answer.
Make sure the final answer report is in the SAME language as the human messages in the message history.

Format the report in clear markdown with proper structure and include source references where appropriate.

<Citation Rules>
- Assign each unique URL a single citation number in your text
- End with ### Sources that lists each source with corresponding numbers
- IMPORTANT: Number sources sequentially without gaps (1,2,3,4...) in the final list regardless of which sources you choose
- Each source should be a separate line item in a list, so that in markdown it is rendered as a list.
- Example format:
  [1] Source Title: URL
  [2] Source Title: URL
- Citations are extremely important. Make sure to include these, and pay a lot of attention to getting these right. Users will often use these citations to look into more information.
</Citation Rules>
</report_instructions>

You have access to a few tools.

## `internet_search`

Use this to run an internet search for a given query. You can specify the number of results, the topic, and whether raw content should be included.
"""

research_sub_agent = {
    "name": "research-agent",
    "description": "Used to research more in depth questions. Only give this researcher one topic at a time. Do not pass multiple sub questions to this researcher. Instead, you should break down a large topic into the necessary components, and then call multiple research agents in parallel, one for each sub question.",
    "prompt": sub_research_prompt,
    "tools": ["internet_search"]
}


# Create the plan-execute subgraph
plan_execute_subgraph = build_plan_execute_agent(
    [internet_search],
    main_prompt,
    subagents=[research_sub_agent],
).with_config({"recursion_limit": 1000})

def call_model_node(state):
    """Simple call model node for direct execution without planning."""
    messages = state.get("messages", [])
    if not messages:
        return state

    # Create a simple direct response agent
    model = get_default_model()
    direct_agent = create_react_agent(
        model,
        tools=[internet_search],
        state_schema=BuilderState
    )

    # Execute directly
    result = direct_agent.invoke(state)
    return result

def route_based_on_complexity(state):
    """Route based on LLM analysis of request complexity."""
    messages = state.get("messages", [])
    if not messages:
        return "plan_execute_subgraph"

    # Get the latest user message
    user_message = None
    for msg in reversed(messages):
        if isinstance(msg, HumanMessage):
            user_message = msg.content
            break

    if not user_message:
        return "plan_execute_subgraph"

    # Use LLM to analyze if planning is needed
    model = get_default_model()
    analysis_prompt = f"""Analyze the following user request and determine if it requires planning or can be handled directly.

User request: "{user_message}"

Consider these factors:
1. Complexity: Does it involve multiple steps or components?
2. Research needed: Does it require gathering information from multiple sources?
3. File operations: Does it involve creating/editing multiple files?
4. Time-sensitive: Is it a simple question that needs immediate response?

Respond with ONLY "PLANNING_NEEDED" or "DIRECT_EXECUTION" based on your analysis.

Examples:
- "What is the weather today?" -> DIRECT_EXECUTION
- "Create a comprehensive research report on AI trends" -> PLANNING_NEEDED
- "Fix this bug in my code" -> DIRECT_EXECUTION (if simple)
- "Build a complete web application with authentication" -> PLANNING_NEEDED"""

    try:
        response = model.invoke([HumanMessage(content=analysis_prompt)])
        analysis_result = response.content.strip().upper()
        needs_planning = "PLANNING_NEEDED" in analysis_result

        if needs_planning:
            return "plan_execute_subgraph"
        else:
            return "call_model"
    except Exception:
        # Default to planning if analysis fails
        return "plan_execute_subgraph"

# Build the main graph with conditional routing
workflow = StateGraph(BuilderState)

# Add nodes
workflow.add_node("planner", plan_execute_subgraph)
workflow.add_node("call_model", call_model_node)

# Add conditional routing directly from START using LLM analysis
workflow.add_conditional_edges(
    START,
    route_based_on_complexity,
    {
        "plan_execute_subgraph": "planner",
        "call_model": "call_model"
    }
)

# Both paths go to END
workflow.add_edge("planner", END)
workflow.add_edge("call_model", END)

# Compile the main graph
graph = workflow.compile()
graph.name = "BondAI"