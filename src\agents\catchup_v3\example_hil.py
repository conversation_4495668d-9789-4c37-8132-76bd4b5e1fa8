# Define subagent with HIL requirements
email_agent = {
    "name": "email-sender",
    "description": "Sends emails to users and stakeholders",
    "prompt": "You are an email specialist...",
    "tools": ["send_email", "draft_email"],
    "require_approval": ["send_email", "send_bulk_email"],  # HIL for these actions
    "approval_threshold": "medium"
}

booking_agent = {
    "name": "booking-agent", 
    "description": "Makes reservations and bookings",
    "prompt": "You are a booking specialist...",
    "tools": ["create_booking", "cancel_booking"],
    "require_approval": ["create_booking"],  # HIL for bookings
    "approval_threshold": "high"
}

# Create agent with HIL-enabled subagents
agent = build_explicit_plan_execute_agent(
    tools=[internet_search],
    instructions="You are a helpful assistant.",
    subagents=[email_agent, booking_agent]
)

# When agent runs, it will pause for human approval
result = agent.invoke({
    "messages": [{"role": "user", "content": "Book a restaurant and send confirmation email"}]
})