# Plan Saving to Mark<PERSON>

This document describes the plan saving functionality that allows the CatchUp v1 agent to save plans to the filesystem in Markdown format for testing and debugging purposes.

## Overview

The `save_plan_to_markdown` tool enables the agent to export the current plan to a well-formatted Markdown file. This is particularly useful for:

- **Testing**: Inspecting generated plans during development
- **Debugging**: Analyzing plan structure and task details
- **Documentation**: Creating readable plan exports
- **Auditing**: Keeping records of agent planning decisions

## Features

### Automatic Markdown Generation
- **Structured Format**: Plans are exported in a clean, hierarchical Markdown format
- **Rich Metadata**: Includes user context, progress metrics, and session information
- **Task Details**: Complete task information including status, priorities, dependencies, and timing
- **Visual Indicators**: Uses emojis and formatting for better readability

### Flexible File Management
- **Custom Filenames**: Optional custom filename specification
- **Automatic Naming**: Timestamp-based naming when no filename provided
- **Directory Organization**: Configurable output directory (defaults to "plans")
- **Safe File Operations**: Automatic directory creation and error handling

## Usage

### Through Agent Conversation

The most natural way to use this feature is by asking the agent to save the plan:

```
User: "Please save the current plan to a markdown file"
User: "Export the plan for testing purposes"
User: "Save this plan as 'restaurant_booking_plan.md'"
```

### Tool Parameters

The `save_plan_to_markdown` tool accepts the following parameters:

- **filename** (optional): Custom filename without extension
- **directory** (optional): Output directory (default: "plans")

### Example Output Structure

```markdown
# CatchUp Agent Plan

**Generated:** 2024-01-15 14:30:25
**Session ID:** session_123

## User Context
- **User ID:** user_456
- **Email:** <EMAIL>
- **Location:** 45.4642, 9.1900

## Progress Overview
- **Total Tasks:** 3
- **Completed:** 1
- **Failed:** 0
- **Remaining:** 2
- **Progress:** 33.3%

## Tasks

### 1. ✅ Find a suitable restaurant `HIGH` 
**Details:**
- **ID:** `task_1`
- **Status:** completed
- **Priority:** high
- **Estimated Duration:** 120 seconds
- **Actual Duration:** 95 seconds
- **Tools Required:** `search_deals`, `get_all_categories`
- **Created:** 2024-01-15T10:30:00Z
- **Completed:** 2024-01-15T10:31:40Z

### 2. 🔄 Book a table for 4 people at 7pm `CRITICAL` **(ACTIVE)**
**Details:**
- **ID:** `task_2`
- **Status:** in_progress
- **Priority:** critical
- **Dependencies:** `task_1`
- **Tools Required:** `create_booking`
- **Created:** 2024-01-15T10:30:00Z
- **Started:** 2024-01-15T10:31:45Z
```

## Implementation Details

### Tool Integration

The tool is automatically bound to the LLM in the `call_model_node` and is available for use during any conversation phase.

### File System Operations

- **Directory Creation**: Automatically creates the output directory if it doesn't exist
- **Filename Handling**: Ensures `.md` extension is added if not provided
- **Error Handling**: Graceful error handling with informative messages
- **Encoding**: Uses UTF-8 encoding for proper character support

### State Requirements

The tool requires the following state information:
- `current_plan`: List of tasks in the current plan
- `progress_metrics`: Progress tracking information
- `active_task_id`: Currently active task identifier
- `user_context`: User information and context
- `session_config`: Session configuration details

## Testing

### Running Tests

Use the provided test script to verify functionality:

```bash
cd src/agents/catchup_v1/examples
python test_plan_saving.py
```

### Test Coverage

The test script covers:
- **End-to-end testing**: Full agent workflow with plan creation and saving
- **Direct tool testing**: Direct tool invocation with mock data
- **File verification**: Checking that files are created correctly
- **Content validation**: Verifying Markdown content structure

### Expected Output

After running tests, you should see:
- `plans/` directory with generated plan files
- `test_plans/` directory with direct test output
- Console output showing test results and file previews

## Configuration

### Default Settings

- **Output Directory**: `plans/`
- **Filename Format**: `plan_{session_id}_{timestamp}.md`
- **File Encoding**: UTF-8

### Customization

You can customize the behavior by:
- Specifying custom filenames in the tool call
- Changing the output directory parameter
- Modifying the Markdown template in `_generate_plan_markdown()`

## Security Considerations

⚠️ **Important**: This feature is intended for testing and development purposes only.

- **File System Access**: The tool writes to the local filesystem
- **Path Validation**: Basic path validation is performed
- **Directory Traversal**: Limited protection against directory traversal attacks
- **Cleanup**: Remember to clean up generated files after testing

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure write permissions for the output directory
2. **Path Issues**: Check that the specified directory path is valid
3. **No Plan Available**: Ensure a plan exists before attempting to save
4. **Encoding Issues**: Files are saved with UTF-8 encoding

### Error Messages

- `"No current plan exists to save"`: Create a plan first using planning functionality
- `"Failed to save plan: [error]"`: Check file permissions and disk space
- `"Directory creation failed"`: Verify write permissions for parent directory

## Future Enhancements

Potential improvements for this feature:
- **Multiple Formats**: Support for JSON, YAML, or other export formats
- **Template Customization**: User-configurable Markdown templates
- **Compression**: Optional ZIP compression for large plans
- **Cloud Storage**: Integration with cloud storage providers
- **Encryption**: Optional encryption for sensitive plan data

---

*This feature is part of the CatchUp v1 agent testing utilities and should be removed before production deployment.*
