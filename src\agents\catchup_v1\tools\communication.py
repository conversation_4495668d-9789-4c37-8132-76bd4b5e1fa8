# """Communication tools for CatchUp v1 agent."""

# from __future__ import annotations

# import uuid
# from datetime import datetime
# from typing import Optional, Dict, Any, Annotated, List
# from langchain_core.tools import tool, InjectedToolCallId
# from langchain_core.messages import ToolMessage
# from langgraph.prebuilt import InjectedState
# from langgraph.types import Command
# from langgraph.config import get_stream_writer

# from agents.catchup_v1.state import CatchUpV1State, CommunicationLog


# @tool
# def send_progress_update(
#     message: str,
#     tool_call_id: Annotated[str, InjectedToolCallId],
#     state: Annotated[CatchUpV1State, InjectedState],
#     communication_type: str = "stream",
#     urgent: bool = False,
# ) -> Command:
#     """Send a progress update to the user via the specified communication channel.
    
#     Args:
#         message: The progress message to send
#         communication_type: Type of communication (stream, email, whatsapp)
#         urgent: Whether this is an urgent update
    
#     Returns:
#         Command to update communication log and send the message
#     """
    
#     user_context = state.get("user_context", {})
#     session_config = state.get("session_config", {})
#     current_time = datetime.now().isoformat()
    
#     # Check if communication is enabled
#     if not session_config.get("communication_enabled", True):
#         return Command(
#             update={
#                 "messages": [
#                     ToolMessage(
#                         content="Communication is disabled for this session. Progress update not sent.",
#                         tool_call_id=tool_call_id
#                     )
#                 ]
#             }
#         )
    
#     # Create communication log entry
#     comm_log = CommunicationLog(
#         id=str(uuid.uuid4()),
#         type=communication_type,
#         recipient=_get_recipient_for_type(communication_type, user_context),
#         subject=f"CatchUp Progress Update" if communication_type == "email" else None,
#         content=message,
#         sent_at=current_time,
#         status="pending",
#         error_message=None
#     )
    
#     # Handle different communication types
#     if communication_type == "stream":
#         # Send via streaming (real-time)
#         try:
#             writer = get_stream_writer()
#             if writer:
#                 writer.write({
#                     "type": "progress_update",
#                     "content": message,
#                     "timestamp": current_time,
#                     "urgent": urgent
#                 })
#             comm_log["status"] = "sent"
#         except Exception as e:
#             comm_log["status"] = "failed"
#             comm_log["error_message"] = str(e)
    
#     elif communication_type in ["email", "whatsapp"]:
#         # Add to pending communications for processing by communication node
#         pending_comm = {
#             "type": communication_type,
#             "recipient": comm_log["recipient"],
#             "subject": comm_log["subject"],
#             "content": message,
#             "urgent": urgent,
#             "created_at": current_time
#         }
        
#         current_pending = state.get("pending_communications", [])
#         updated_pending = current_pending + [pending_comm]
        
#         return Command(
#             update={
#                 "communication_log": [comm_log],
#                 "pending_communications": updated_pending,
#                 "messages": [
#                     ToolMessage(
#                         content=f"Progress update queued for {communication_type} delivery: {message[:100]}...",
#                         tool_call_id=tool_call_id
#                     )
#                 ]
#             }
#         )
    
#     # Update progress metrics
#     current_metrics = state.get("progress_metrics", {})
#     communication_sent = current_metrics.get("communication_sent", [])
#     updated_metrics = {
#         **current_metrics,
#         "communication_sent": communication_sent + [communication_type]
#     }
    
#     return Command(
#         update={
#             "communication_log": [comm_log],
#             "progress_metrics": updated_metrics,
#             "messages": [
#                 ToolMessage(
#                     content=f"Progress update sent via {communication_type}: {message[:100]}...",
#                     tool_call_id=tool_call_id
#                 )
#             ]
#         }
#     )


# @tool
# def send_plan_summary(
#     state: Annotated[CatchUpV1State, InjectedState],
#     tool_call_id: Annotated[str, InjectedToolCallId],
#     include_completed: bool = True,
#     include_pending: bool = True,
#     communication_type: str = "email",
# ) -> Command:
#     """Send a comprehensive plan summary to the user.
    
#     Args:
#         include_completed: Whether to include completed tasks
#         include_pending: Whether to include pending tasks
#         communication_type: Type of communication (email, whatsapp, stream)
    
#     Returns:
#         Command to send the plan summary
#     """
    
#     current_plan = state.get("current_plan", [])
#     progress_metrics = state.get("progress_metrics", {})
#     user_context = state.get("user_context", {})
    
#     if not current_plan:
#         return Command(
#             update={
#                 "messages": [
#                     ToolMessage(
#                         content="No current plan exists to summarize.",
#                         tool_call_id=tool_call_id
#                     )
#                 ]
#             }
#         )
    
#     # Build plan summary
#     summary_parts = []
    
#     # Header
#     summary_parts.append("🎯 CatchUp Plan Summary")
#     summary_parts.append("=" * 30)
    
#     # Progress overview
#     total_tasks = progress_metrics.get("total_tasks", len(current_plan))
#     completed_tasks = progress_metrics.get("completed_tasks", 0)
#     failed_tasks = progress_metrics.get("failed_tasks", 0)
    
#     summary_parts.append(f"📊 Progress: {completed_tasks}/{total_tasks} tasks completed")
#     if failed_tasks > 0:
#         summary_parts.append(f"❌ Failed: {failed_tasks} tasks")
    
#     completion_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
#     summary_parts.append(f"📈 Completion: {completion_percentage:.1f}%")
    
#     # Task details
#     if include_completed:
#         completed_tasks_list = [task for task in current_plan if task["status"] == "completed"]
#         if completed_tasks_list:
#             summary_parts.append("\n✅ Completed Tasks:")
#             for task in completed_tasks_list:
#                 duration_info = ""
#                 if task.get("actual_duration"):
#                     duration_info = f" ({task['actual_duration']}s)"
#                 summary_parts.append(f"  • {task['content']}{duration_info}")
    
#     if include_pending:
#         pending_tasks = [task for task in current_plan if task["status"] == "pending"]
#         if pending_tasks:
#             summary_parts.append("\n⏳ Pending Tasks:")
#             for task in pending_tasks:
#                 priority_info = f" [{task['priority'].upper()}]" if task.get("priority") != "medium" else ""
#                 summary_parts.append(f"  • {task['content']}{priority_info}")
    
#     # Failed tasks
#     failed_tasks_list = [task for task in current_plan if task["status"] == "failed"]
#     if failed_tasks_list:
#         summary_parts.append("\n❌ Failed Tasks:")
#         for task in failed_tasks_list:
#             error_info = f" - {task['error_message']}" if task.get("error_message") else ""
#             summary_parts.append(f"  • {task['content']}{error_info}")
    
#     # Footer
#     summary_parts.append(f"\n📅 Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
#     plan_summary = "\n".join(summary_parts)
    
#     # Send the summary
#     return send_progress_update(
#         message=plan_summary,
#         communication_type=communication_type,
#         urgent=False,
#         state=state,
#         tool_call_id=tool_call_id
#     )


# def _get_recipient_for_type(communication_type: str, user_context: Dict[str, Any]) -> str:
#     """Get the appropriate recipient address for the communication type."""
#     if communication_type == "email":
#         return user_context.get("email_address", "")
#     elif communication_type == "whatsapp":
#         return user_context.get("phone_number", "")
#     else:
#         return user_context.get("user_id", "")


# @tool
# def stream_status_update(
#     state: Annotated[CatchUpV1State, InjectedState],
#     tool_call_id: Annotated[str, InjectedToolCallId],
#     status: str,
#     details: Optional[str] = None,
#     progress_percentage: Optional[float] = None,

# ) -> Command:
#     """Stream a status update with structured data for UI components.
    
#     Args:
#         status: Current status (planning, executing, communicating, etc.)
#         details: Additional details about the current operation
#         progress_percentage: Overall progress percentage (0-100)
    
#     Returns:
#         Command to stream the status update
#     """
    
#     session_config = state.get("session_config", {})
    
#     if not session_config.get("streaming_enabled", True):
#         return Command(
#             update={
#                 "messages": [
#                     ToolMessage(
#                         content="Streaming is disabled for this session.",
#                         tool_call_id=tool_call_id
#                     )
#                 ]
#             }
#         )
    
#     try:
#         writer = get_stream_writer()
#         if writer:
#             writer.write({
#                 "type": "status_update",
#                 "status": status,
#                 "details": details,
#                 "progress_percentage": progress_percentage,
#                 "timestamp": datetime.now().isoformat()
#             })
        
#         return Command(
#             update={
#                 "messages": [
#                     ToolMessage(
#                         content=f"Status update streamed: {status}" + (f" - {details}" if details else ""),
#                         tool_call_id=tool_call_id
#                     )
#                 ]
#             }
#         )
    
#     except Exception as e:
#         return Command(
#             update={
#                 "messages": [
#                     ToolMessage(
#                         content=f"Failed to stream status update: {str(e)}",
#                         tool_call_id=tool_call_id
#                     )
#                 ]
#             }
#         )
