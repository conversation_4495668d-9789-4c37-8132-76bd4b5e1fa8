from typing import TypedDict, Literal, Optional
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.types import Command, interrupt

class HILRequest(TypedDict):
    subagent_name: str
    action_description: str
    proposed_action: str
    reasoning: str
    approval_required: bool

class HILResponse(TypedDict):
    approved: bool
    feedback: Optional[str]
    modified_action: Optional[str]

@tool
def request_human_approval(
    action_description: str,
    proposed_action: str,
    reasoning: str,
    subagent_name: str = "unknown"
) -> str:
    """Request human approval for critical actions before execution."""
    
    # Create HIL request
    hil_request = HILRequest(
        subagent_name=subagent_name,
        action_description=action_description,
        proposed_action=proposed_action,
        reasoning=reasoning,
        approval_required=True
    )
    
    # Interrupt execution for human input
    raise interrupt({
        "type": "human_approval_required",
        "request": hil_request,
        "message": f"🤖 {subagent_name} requests approval:\n\n"
                  f"**Action**: {action_description}\n"
                  f"**Proposed**: {proposed_action}\n"
                  f"**Reasoning**: {reasoning}\n\n"
                  f"Do you approve? (yes/no/modify)"
    })